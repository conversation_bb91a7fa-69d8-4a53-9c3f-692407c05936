import Image from "next/image"
import Link from "next/link"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"

export default function AstrologiPlaneter() {
  const planets = [
    {
      name: "Solen",
      description: "Representerar din grundläggande identitet, medvetna jag, kreativa livsenergi och livssyfte.",
      rules: "Lejonet",
      element: "Eld",
      image: "/images/solen.png",
      link: "/astrologi-lara/planeter/solen",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      description: "Representerar dina känslor, instinkter, undermedvetna, emotionella behov och inre trygghet.",
      rules: "Kräftan",
      element: "Vatten",
      image: "/images/manen.png",
      link: "/astrologi-lara/planeter/manen",
    },
    {
      name: "Merkurius",
      description: "Representerar ditt intellekt, kommunikation, tankeprocesser, inlärning och informationsutbyte.",
      rules: "Tvillingarna, Jungfrun",
      element: "Luft/Jord",
      image: "/images/merkurius.png",
      link: "/astrologi-lara/planeter/merkurius",
    },
    {
      name: "Venus",
      description: "Representerar kärlek, skönhet, harmoni, värderingar, relationer och estetik.",
      rules: "Oxen, Vågen",
      element: "Jord/Luft",
      image: "/images/venus.png",
      link: "/astrologi-lara/planeter/venus",
    },
    {
      name: "Mars",
      description: "Representerar handlingskraft, energi, passion, mod, sexualitet och självhävdelse.",
      rules: "Väduren, (Skorpionen)",
      element: "Eld",
      image: "/images/mars.png",
      link: "/astrologi-lara/planeter/mars",
    },
    {
      name: "Jupiter",
      description: "Representerar expansion, tillväxt, optimism, lycka, visdom, filosofi och överflöd.",
      rules: "Skytten, (Fiskarna)",
      element: "Eld",
      image: "/images/jupiter.png",
      link: "/astrologi-lara/planeter/jupiter",
    },
    {
      name: "Saturnus",
      description: "Representerar struktur, disciplin, ansvar, begränsningar, tid, mognad och auktoritet.",
      rules: "Stenbocken, (Vattumannen)",
      element: "Jord",
      image: "/images/saturnus.png",
      link: "/astrologi-lara/planeter/saturnus",
    },
    {
      name: "Uranus",
      description: "Representerar förändring, originalitet, uppror, innovation, frigörelse och det oväntade.",
      rules: "Vattumannen",
      element: "Luft",
      image: "/images/uranus.png",
      link: "/astrologi-lara/planeter/uranus",
    },
    {
      name: "Neptunus",
      description: "Representerar intuition, drömmar, andlighet, mystik, illusion och transcendens.",
      rules: "Fiskarna",
      element: "Vatten",
      image: "/images/neptunus.png",
      link: "/astrologi-lara/planeter/neptunus",
    },
    {
      name: "Pluto",
      description: "Representerar transformation, makt, intensitet, död och pånyttfödelse, och det undermedvetna.",
      rules: "Skorpionen",
      element: "Vatten",
      image: "/images/pluto.png",
      link: "/astrologi-lara/planeter/pluto",
    },
  ]

  const planetGroups = {
    personal: planets.slice(0, 5),
    social: planets.slice(5, 7),
    transpersonal: planets.slice(7, 10),
  }

  return (
    <>
      {/* Fixed star background */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
        <div className="stars-small opacity-30"></div>
      </div>

      <main className="relative z-10 container px-4 py-28 mx-auto">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="font-display text-4xl md:text-5xl lg:text-6xl mb-6 text-white cosmic-title leading-tight">Planeterna i Astrologin</h1>
            <p className="text-lg md:text-xl text-slate-300 max-w-3xl mx-auto drop-shadow-md">
              Utforska planeternas betydelse i astrologin och hur deras energier påverkar olika aspekter av våra liv och
              personligheter.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-16">
            <div className="flex flex-col justify-center">
              <h2 className="font-display text-2xl md:text-3xl mb-6 text-white">Planeternas Roll i Astrologin</h2>
              <p className="text-slate-300 mb-6 leading-relaxed">
                I astrologin representerar planeterna olika energier och principer som påverkar olika aspekter av våra liv
                och personligheter. Varje planet har sin egen unika karaktär och påverkar oss på olika sätt beroende på
                dess position i födelsehoroskopet.
              </p>
              <p className="text-slate-300 mb-6 leading-relaxed">
                Planeterna fungerar som aktörer på den kosmiska scenen, och deras placering i stjärntecken och hus visar
                hur och var deras energier kommer till uttryck i våra liv.
              </p>
            </div>
            <div className="relative h-64 md:h-auto rounded-lg overflow-hidden border border-[#6e56cf]/30 shadow-lg shadow-primary/10">
              <Image
                src="/images/planeter-hero.png"
                alt="Planeterna i astrologin"
                fill
                className="object-cover"
              />
            </div>
          </div>

          <Tabs defaultValue="all" className="mb-16">
            <TabsList className="grid w-full grid-cols-4 mb-8 bg-[#1a1333]/80 p-1 rounded-lg border border-[#6e56cf]/30">
              <TabsTrigger value="all" className="data-[state=active]:bg-[#6e56cf] data-[state=active]:text-white text-slate-300 transition-all duration-300">Alla Planeter</TabsTrigger>
              <TabsTrigger value="personal" className="data-[state=active]:bg-[#6e56cf] data-[state=active]:text-white text-slate-300 transition-all duration-300">Personliga</TabsTrigger>
              <TabsTrigger value="social" className="data-[state=active]:bg-[#6e56cf] data-[state=active]:text-white text-slate-300 transition-all duration-300">Sociala</TabsTrigger>
              <TabsTrigger value="transpersonal" className="data-[state=active]:bg-[#6e56cf] data-[state=active]:text-white text-slate-300 transition-all duration-300">Transpersonliga</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="pt-6">
              <h2 className="font-display text-2xl md:text-3xl mb-8 text-center text-white">Alla Planeter</h2>

              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
                {planets.map((planet) => (
                  <Card key={planet.name} className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10 group">
                    <div className="relative h-40 w-full overflow-hidden">
                      <Image src={planet.image} alt={planet.name} fill className="object-cover group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    <CardHeader className="p-4 pb-0">
                      <CardTitle className="text-white font-display">{planet.name}</CardTitle>
                      <CardDescription className="text-slate-300 text-xs">
                        Styr: {planet.rules} | Element: {planet.element}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="p-4 pt-2">
                      <p className="text-sm text-slate-300 leading-relaxed">{planet.description}</p>
                    </CardContent>
                    <CardFooter className="p-4 pt-0">
                      <Link
                        href={planet.link}
                        className="w-full text-center py-2 px-4 bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 border border-[#6e56cf]/30 rounded-md text-white transition-all duration-300 inline-block font-medium"
                      >
                        Läs mer
                      </Link>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="personal" className="pt-6">
              <h2 className="font-display text-2xl md:text-3xl mb-6 text-center text-white">Personliga Planeter</h2>
              <p className="mb-8 text-center text-slate-300 max-w-3xl mx-auto leading-relaxed">
                De personliga planeterna rör sig relativt snabbt genom zodiaken och påverkar våra personliga egenskaper,
                dagliga liv och individuella uttryck.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {planetGroups.personal.map((planet, index) => (
                  <Card key={index} className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10 group">
                    <div className="relative h-48 w-full overflow-hidden">
                      <Image
                        src={planet.image || "/placeholder.svg"}
                        alt={planet.name}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                    </div>
                    <CardHeader className="p-4">
                      <CardTitle className="text-white font-display">{planet.name}</CardTitle>
                      <CardDescription className="text-slate-300 text-sm">
                        Styr: {planet.rules} | Element: {planet.element}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="p-4 pt-0">
                      <p className="text-sm text-slate-300 leading-relaxed">{planet.description}</p>
                    </CardContent>
                    <CardFooter className="p-4 pt-0">
                      <Link
                        href={planet.link}
                        className="w-full text-center py-2 px-4 bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 border border-[#6e56cf]/30 rounded-md text-white transition-all duration-300 inline-block font-medium"
                      >
                        Läs mer
                      </Link>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="social" className="pt-6">
              <h2 className="font-display text-2xl md:text-3xl mb-6 text-center text-white">Sociala Planeter</h2>
              <p className="mb-8 text-center text-slate-300 max-w-3xl mx-auto leading-relaxed">
                De sociala planeterna rör sig långsammare genom zodiaken och påverkar våra sociala relationer, värderingar,
                och hur vi interagerar med samhället.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {planetGroups.social.map((planet, index) => (
                  <Card key={index} className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10 group">
                    <div className="relative h-48 w-full overflow-hidden">
                      <Image
                        src={planet.image || "/placeholder.svg"}
                        alt={planet.name}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                    </div>
                    <CardHeader className="p-4">
                      <CardTitle className="text-white font-display">{planet.name}</CardTitle>
                      <CardDescription className="text-slate-300 text-sm">
                        Styr: {planet.rules} | Element: {planet.element}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="p-4 pt-0">
                      <p className="text-sm text-slate-300 leading-relaxed">{planet.description}</p>
                    </CardContent>
                    <CardFooter className="p-4 pt-0">
                      <Link
                        href={planet.link}
                        className="w-full text-center py-2 px-4 bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 border border-[#6e56cf]/30 rounded-md text-white transition-all duration-300 inline-block font-medium"
                      >
                        Läs mer
                      </Link>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="transpersonal" className="pt-6">
              <h2 className="font-display text-2xl md:text-3xl mb-6 text-center text-white">Transpersonliga Planeter</h2>
              <p className="mb-8 text-center text-slate-300 max-w-3xl mx-auto leading-relaxed">
                De transpersonliga planeterna rör sig mycket långsamt genom zodiaken och påverkar hela generationer,
                kollektiva trender och djupare transformationsprocesser.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {planetGroups.transpersonal.map((planet, index) => (
                  <Card key={index} className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10 group">
                    <div className="relative h-48 w-full overflow-hidden">
                      <Image
                        src={planet.image || "/placeholder.svg"}
                        alt={planet.name}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                    </div>
                    <CardHeader className="p-4">
                      <CardTitle className="text-white font-display">{planet.name}</CardTitle>
                      <CardDescription className="text-slate-300 text-sm">
                        Styr: {planet.rules} | Element: {planet.element}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="p-4 pt-0">
                      <p className="text-sm text-slate-300 leading-relaxed">{planet.description}</p>
                    </CardContent>
                    <CardFooter className="p-4 pt-0">
                      <Link
                        href={planet.link}
                        className="w-full text-center py-2 px-4 bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 border border-[#6e56cf]/30 rounded-md text-white transition-all duration-300 inline-block font-medium"
                      >
                        Läs mer
                      </Link>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>

          <div className="mb-16">
            <h2 className="font-display text-2xl md:text-3xl mb-6 text-center text-white">Planeternas Cykler</h2>
            <p className="mb-8 text-center text-slate-300 max-w-3xl mx-auto leading-relaxed">
              Planeterna rör sig i olika hastigheter runt zodiaken, vilket skapar cykler som påverkar både individuella
              liv och kollektiva trender.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-[#1a1333]/80 border border-[#6e56cf]/30 p-6 rounded-lg shadow-lg shadow-primary/10 hover:border-[#6e56cf]/40 transition-all duration-300">
                <h3 className="font-display text-lg mb-4 text-white">Snabba Cykler</h3>
                <p className="text-sm text-slate-300 leading-relaxed">
                  Månen (28 dagar), Merkurius (88 dagar), Venus (225 dagar) och Mars (687 dagar) har relativt snabba
                  cykler som påverkar våra dagliga liv, känslor, kommunikation och handlingar.
                </p>
              </div>

              <div className="bg-[#1a1333]/80 border border-[#6e56cf]/30 p-6 rounded-lg shadow-lg shadow-primary/10 hover:border-[#6e56cf]/40 transition-all duration-300">
                <h3 className="font-display text-lg mb-4 text-white">Mellanliggande Cykler</h3>
                <p className="text-sm text-slate-300 leading-relaxed">
                  Jupiter (12 år) och Saturnus (29 år) har längre cykler som markerar viktiga utvecklingsfaser i våra liv,
                  relaterade till tillväxt, expansion, struktur och ansvar.
                </p>
              </div>

              <div className="bg-[#1a1333]/80 border border-[#6e56cf]/30 p-6 rounded-lg shadow-lg shadow-primary/10 hover:border-[#6e56cf]/40 transition-all duration-300">
                <h3 className="font-display text-lg mb-4 text-white">Långsamma Cykler</h3>
                <p className="text-sm text-slate-300 leading-relaxed">
                  Uranus (84 år), Neptunus (165 år) och Pluto (248 år) har mycket långa cykler som påverkar hela
                  generationer och markerar större kulturella och samhälleliga förändringar.
                </p>
              </div>
            </div>
          </div>

          <div className="mb-16">
            <h2 className="font-display text-2xl md:text-3xl mb-6 text-white">Planeterna i Ditt Horoskop</h2>
            <p className="text-slate-300 mb-6 leading-relaxed">
              I ditt födelsehoroskop visar planeternas positioner i stjärntecken och hus hur dessa energier kommer till
              uttryck i ditt liv. Till exempel:
            </p>
            <ul className="list-disc pl-5 space-y-3 text-slate-300 mb-6">
              <li>
                <span className="text-white font-medium">Solen</span> visar din grundläggande identitet och livssyfte.
              </li>
              <li>
                <span className="text-white font-medium">Månen</span> visar dina känslomässiga behov och instinktiva reaktioner.
              </li>
              <li>
                <span className="text-white font-medium">Merkurius</span> visar hur du tänker och kommunicerar.
              </li>
              <li>
                <span className="text-white font-medium">Venus</span> visar vad du värdesätter och hur du uttrycker kärlek.
              </li>
              <li>
                <span className="text-white font-medium">Mars</span> visar hur du agerar och hävdar dig själv.
              </li>
            </ul>
            <p className="text-slate-300 mb-6 leading-relaxed">
              Planeternas aspekter (vinklar) till varandra visar hur dessa olika delar av din personlighet interagerar med
              varandra. Till exempel kan en harmonisk aspekt mellan Solen och Jupiter indikera ett naturligt
              självförtroende och optimism, medan en utmanande aspekt mellan Mars och Saturnus kan indikera en inre
              konflikt mellan handlingskraft och försiktighet.
            </p>
            <p className="text-slate-300 mb-6 leading-relaxed">
              Fortsätt din astrologiska resa genom att utforska våra andra sektioner om{" "}
              <Link href="/astrologi-lara/grunderna" className="text-[#a78bfa] hover:text-white transition-colors duration-300 font-medium">
                grunderna
              </Link>
              ,{" "}
              <Link href="/astrologi-lara/husen" className="text-[#a78bfa] hover:text-white transition-colors duration-300 font-medium">
                husen
              </Link>{" "}
              och{" "}
              <Link href="/astrologi-lara/aspekter" className="text-[#a78bfa] hover:text-white transition-colors duration-300 font-medium">
                aspekter
              </Link>
              , eller fördjupa dig i de specifika planeterna genom länkarna ovan.
            </p>
          </div>
        </div>
      </main>
    </>
  )
}
